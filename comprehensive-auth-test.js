const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function comprehensiveAuthTest() {
    console.log('🔒 Comprehensive Authentication & Authorization Security Test');
    console.log('===========================================================\n');

    const results = {
        passed: 0,
        failed: 0,
        warnings: 0
    };

    // Test 1: JWT Token Expiration (if applicable)
    console.log('1️⃣ Testing JWT Token Security...');
    try {
        // Test with malformed token
        await axios.get(`${BASE_URL}/api/auth/me`, {
            headers: { Authorization: 'Bearer malformed.token.here' }
        });
        console.log('❌ SECURITY ISSUE: Malformed token accepted!');
        results.failed++;
    } catch (error) {
        if (error.response?.status === 401) {
            console.log('✅ Malformed token correctly rejected');
            results.passed++;
        } else {
            console.log(`⚠️  Unexpected response: ${error.response?.status}`);
            results.warnings++;
        }
    }

    // Test 2: SQL Injection in Login
    console.log('\n2️⃣ Testing SQL Injection Protection...');
    try {
        await axios.post(`${BASE_URL}/api/auth/login`, {
            email: "<EMAIL>' OR '1'='1",
            password: "anything"
        });
        console.log('❌ SECURITY ISSUE: SQL injection might be possible!');
        results.failed++;
    } catch (error) {
        if (error.response?.status === 401) {
            console.log('✅ SQL injection attempt correctly rejected');
            results.passed++;
        } else {
            console.log(`⚠️  Unexpected response: ${error.response?.status}`);
            results.warnings++;
        }
    }

    // Test 3: Rate Limiting
    console.log('\n3️⃣ Testing Rate Limiting...');
    let rateLimitHit = false;
    for (let i = 0; i < 5; i++) {
        try {
            await axios.post(`${BASE_URL}/api/auth/login`, {
                email: '<EMAIL>',
                password: 'wrongpassword'
            });
        } catch (error) {
            if (error.response?.status === 429) {
                console.log('✅ Rate limiting is working');
                rateLimitHit = true;
                results.passed++;
                break;
            }
        }
    }
    if (!rateLimitHit) {
        console.log('⚠️  Rate limiting not triggered in 5 attempts (may be configured for higher limits)');
        results.warnings++;
    }

    // Test 4: Password Security
    console.log('\n4️⃣ Testing Password Security Requirements...');
    const weakPasswords = [
        'password',
        '123456',
        'abc123',
        'Password',  // Missing number
        'password123',  // Missing uppercase
        'PASSWORD123'   // Missing lowercase
    ];

    let weakPasswordsRejected = 0;
    for (const weakPassword of weakPasswords) {
        try {
            await axios.post(`${BASE_URL}/api/auth/register`, {
                email: `test${Date.now()}@example.com`,
                password: weakPassword,
                first_name: 'Test',
                last_name: 'User'
            });
            console.log(`❌ SECURITY ISSUE: Weak password "${weakPassword}" was accepted!`);
            results.failed++;
        } catch (error) {
            if (error.response?.status === 400) {
                weakPasswordsRejected++;
            }
        }
    }
    
    if (weakPasswordsRejected === weakPasswords.length) {
        console.log('✅ All weak passwords correctly rejected');
        results.passed++;
    } else {
        console.log(`⚠️  ${weakPasswordsRejected}/${weakPasswords.length} weak passwords rejected`);
        results.warnings++;
    }

    // Test 5: Role Escalation
    console.log('\n5️⃣ Testing Role Escalation Protection...');
    try {
        // Register a regular user
        const userResponse = await axios.post(`${BASE_URL}/api/auth/register`, {
            email: `roletest${Date.now()}@example.com`,
            password: 'TestPassword123',
            first_name: 'Role',
            last_name: 'Test'
        });

        const userToken = userResponse.data.data.token;
        
        // Try to access admin endpoints
        const adminEndpoints = [
            '/api/admin/dashboard',
            '/api/admin/users',
            '/api/admin/orders'
        ];

        let allEndpointsBlocked = true;
        for (const endpoint of adminEndpoints) {
            try {
                await axios.get(`${BASE_URL}${endpoint}`, {
                    headers: { Authorization: `Bearer ${userToken}` }
                });
                console.log(`❌ SECURITY ISSUE: Regular user can access ${endpoint}!`);
                allEndpointsBlocked = false;
                results.failed++;
            } catch (error) {
                if (error.response?.status !== 403) {
                    console.log(`⚠️  Unexpected response for ${endpoint}: ${error.response?.status}`);
                    results.warnings++;
                }
            }
        }

        if (allEndpointsBlocked) {
            console.log('✅ All admin endpoints properly protected from regular users');
            results.passed++;
        }

    } catch (error) {
        console.log('⚠️  Could not test role escalation due to registration failure');
        results.warnings++;
    }

    // Test 6: CORS Headers
    console.log('\n6️⃣ Testing CORS Configuration...');
    try {
        const response = await axios.get(`${BASE_URL}/health`);
        const corsHeader = response.headers['access-control-allow-origin'];
        if (corsHeader) {
            if (corsHeader === '*') {
                console.log('⚠️  CORS allows all origins (*)');
                results.warnings++;
            } else {
                console.log(`✅ CORS properly configured: ${corsHeader}`);
                results.passed++;
            }
        } else {
            console.log('⚠️  No CORS headers found');
            results.warnings++;
        }
    } catch (error) {
        console.log('⚠️  Could not test CORS configuration');
        results.warnings++;
    }

    // Test 7: Security Headers
    console.log('\n7️⃣ Testing Security Headers...');
    try {
        const response = await axios.get(`${BASE_URL}/health`);
        const securityHeaders = [
            'content-security-policy',
            'x-frame-options',
            'x-content-type-options',
            'strict-transport-security'
        ];

        let securityHeadersPresent = 0;
        securityHeaders.forEach(header => {
            if (response.headers[header]) {
                securityHeadersPresent++;
            }
        });

        if (securityHeadersPresent >= 3) {
            console.log(`✅ Good security headers present (${securityHeadersPresent}/${securityHeaders.length})`);
            results.passed++;
        } else {
            console.log(`⚠️  Limited security headers (${securityHeadersPresent}/${securityHeaders.length})`);
            results.warnings++;
        }
    } catch (error) {
        console.log('⚠️  Could not test security headers');
        results.warnings++;
    }

    // Summary
    console.log('\n📊 Security Test Summary');
    console.log('========================');
    console.log(`✅ Tests Passed: ${results.passed}`);
    console.log(`❌ Tests Failed: ${results.failed}`);
    console.log(`⚠️  Warnings: ${results.warnings}`);
    
    if (results.failed === 0) {
        console.log('\n🎉 No critical security issues found!');
        if (results.warnings > 0) {
            console.log('💡 Consider addressing the warnings for enhanced security.');
        }
    } else {
        console.log('\n🚨 Critical security issues detected! Please address them immediately.');
    }

    return results;
}

comprehensiveAuthTest().catch(console.error);
