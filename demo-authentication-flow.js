const http = require('http');

// Demo the complete authentication flow
async function demoAuthenticationFlow() {
    console.log('🎭 Luxe Fashion Authentication Flow Demo');
    console.log('=========================================');

    try {
        // Step 1: Register a new user
        console.log('\n👤 Step 1: Registering a new user...');
        const newUser = await registerUser();
        console.log(`✅ User registered: ${newUser.user.email}`);
        console.log(`   Role: ${newUser.user.role}`);
        console.log(`   Token: ${newUser.token.substring(0, 20)}...`);

        // Step 2: Test user access to regular endpoints
        console.log('\n🔓 Step 2: Testing user access to regular endpoints...');
        const userProfile = await testEndpoint('/api/auth/me', newUser.token);
        console.log(`✅ User can access profile: ${userProfile.success}`);

        // Step 3: Test user access to admin endpoints (should fail)
        console.log('\n🚫 Step 3: Testing user access to admin endpoints (should fail)...');
        const adminAccess = await testEndpoint('/api/admin/dashboard', newUser.token);
        console.log(`❌ User cannot access admin dashboard: ${!adminAccess.success} (Expected)`);

        // Step 4: Login as admin
        console.log('\n👑 Step 4: Logging in as admin...');
        const adminUser = await loginAdmin();
        console.log(`✅ Admin logged in: ${adminUser.user.email}`);
        console.log(`   Role: ${adminUser.user.role}`);
        console.log(`   Token: ${adminUser.token.substring(0, 20)}...`);

        // Step 5: Test admin access to admin endpoints
        console.log('\n🔐 Step 5: Testing admin access to admin endpoints...');
        const adminDashboard = await testEndpoint('/api/admin/dashboard', adminUser.token);
        console.log(`✅ Admin can access dashboard: ${adminDashboard.success}`);

        const adminUsers = await testEndpoint('/api/admin/users', adminUser.token);
        console.log(`✅ Admin can access user management: ${adminUsers.success}`);

        const adminOrders = await testEndpoint('/api/admin/orders', adminUser.token);
        console.log(`✅ Admin can access order management: ${adminOrders.success}`);

        // Step 6: Test role-based access summary
        console.log('\n📊 Step 6: Role-based Access Summary');
        console.log('=====================================');
        console.log('Regular User Access:');
        console.log(`   ✅ Profile: ${userProfile.success}`);
        console.log(`   ❌ Admin Dashboard: ${!adminAccess.success}`);
        console.log('\nAdmin User Access:');
        console.log(`   ✅ Profile: Available`);
        console.log(`   ✅ Admin Dashboard: ${adminDashboard.success}`);
        console.log(`   ✅ User Management: ${adminUsers.success}`);
        console.log(`   ✅ Order Management: ${adminOrders.success}`);

        console.log('\n🎉 Authentication Flow Demo Complete!');
        console.log('\n🌐 Try it yourself:');
        console.log('   1. Visit: http://localhost:3003');
        console.log('   2. Register a new account');
        console.log('   3. Login with admin credentials');
        console.log('   4. Access the admin dashboard');

    } catch (error) {
        console.error('\n❌ Demo failed:', error.message);
    }
}

// Helper functions
async function registerUser() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            email: `demo${Date.now()}@luxefashion.com`,
            password: 'DemoPassword123',
            first_name: 'Demo',
            last_name: 'User'
        });

        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/auth/register',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    if (res.statusCode === 201 && jsonData.success) {
                        resolve(jsonData.data);
                    } else {
                        reject(new Error(jsonData.message || 'Registration failed'));
                    }
                } catch (e) {
                    reject(new Error('Invalid response format'));
                }
            });
        });

        req.on('error', reject);
        req.write(postData);
        req.end();
    });
}

async function loginAdmin() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            email: '<EMAIL>',
            password: 'admin123'
        });

        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/auth/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    if (res.statusCode === 200 && jsonData.success) {
                        resolve(jsonData.data);
                    } else {
                        reject(new Error(jsonData.message || 'Login failed'));
                    }
                } catch (e) {
                    reject(new Error('Invalid response format'));
                }
            });
        });

        req.on('error', reject);
        req.write(postData);
        req.end();
    });
}

async function testEndpoint(path, token) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: path,
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    resolve({
                        success: res.statusCode === 200 && jsonData.success,
                        status: res.statusCode,
                        data: jsonData
                    });
                } catch (e) {
                    resolve({
                        success: false,
                        status: res.statusCode,
                        error: 'Invalid JSON response'
                    });
                }
            });
        });

        req.on('error', () => {
            resolve({
                success: false,
                error: 'Connection failed'
            });
        });

        req.end();
    });
}

// Run the demo
demoAuthenticationFlow().catch(console.error);
