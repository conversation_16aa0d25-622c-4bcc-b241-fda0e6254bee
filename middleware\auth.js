const jwt = require('jsonwebtoken');
const { executeQuery } = require('../config/database');

// Protect routes - require authentication
const protect = async (req, res, next) => {
  try {
    let token;

    // Get token from header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    // Make sure token exists
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    try {
      // Verify token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);

      // Additional security: Check if token is not too old (optional)
      const tokenAge = Date.now() / 1000 - decoded.iat;
      const maxAge = 7 * 24 * 60 * 60; // 7 days in seconds
      if (tokenAge > maxAge) {
        return res.status(401).json({
          success: false,
          message: 'Token has expired'
        });
      }

      // Get user from database
      const user = await executeQuery(
        'SELECT id, email, first_name, last_name, role, is_verified FROM users WHERE id = ?',
        [decoded.id]
      );

      if (!user.length) {
        return res.status(401).json({
          success: false,
          message: 'No user found with this token'
        });
      }

      // Check if user account is verified (optional security enhancement)
      // Skip verification check in development environment
      if (!user[0].is_verified && process.env.NODE_ENV === 'production') {
        return res.status(401).json({
          success: false,
          message: 'Account not verified'
        });
      }

      req.user = user[0];
      next();
    } catch (error) {
      // Log security events for monitoring
      console.log(`Authentication failed: ${error.message} - IP: ${req.ip}`);
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }
  } catch (error) {
    next(error);
  }
};

// Optional authentication - doesn't require token but sets user if present
const optionalAuth = async (req, res, next) => {
  try {
    let token;

    // Get token from header
    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
      token = req.headers.authorization.split(' ')[1];
    }

    if (token) {
      try {
        // Verify token
        const decoded = jwt.verify(token, process.env.JWT_SECRET);

        // Get user from database
        const user = await executeQuery(
          'SELECT id, email, first_name, last_name, role, is_verified FROM users WHERE id = ?',
          [decoded.id]
        );

        if (user.length) {
          req.user = user[0];
        }
      } catch (error) {
        // Token invalid, but continue without user
        console.log('Invalid token in optional auth:', error.message);
      }
    }

    next();
  } catch (error) {
    next(error);
  }
};

// Grant access to specific roles
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Not authorized to access this route'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'User role is not authorized to access this route'
      });
    }

    next();
  };
};

module.exports = {
  protect,
  optionalAuth,
  authorize
};
