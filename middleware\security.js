const rateLimit = require('express-rate-limit');

// Enhanced rate limiting for authentication endpoints
const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // Limit each IP to 5 requests per windowMs for auth endpoints
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip successful requests
  skipSuccessfulRequests: true,
  // Custom key generator to include user agent for better tracking
  keyGenerator: (req) => {
    return `${req.ip}-${req.get('User-Agent')}`;
  }
});

// Strict rate limiting for admin endpoints
const adminRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 20, // Limit each IP to 20 requests per windowMs for admin endpoints
  message: {
    success: false,
    message: 'Too many admin requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false
});

// Security logging middleware
const securityLogger = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Log failed authentication attempts
    if (res.statusCode === 401 || res.statusCode === 403) {
      console.log(`[SECURITY] ${new Date().toISOString()} - ${res.statusCode} - ${req.method} ${req.path} - IP: ${req.ip} - User-Agent: ${req.get('User-Agent')}`);
      
      // Parse response data to get more details
      try {
        const responseData = JSON.parse(data);
        if (responseData.message) {
          console.log(`[SECURITY] Reason: ${responseData.message}`);
        }
      } catch (e) {
        // Ignore JSON parse errors
      }
    }
    
    // Log successful admin access
    if (req.path.startsWith('/api/admin') && res.statusCode === 200) {
      console.log(`[ADMIN ACCESS] ${new Date().toISOString()} - ${req.method} ${req.path} - User: ${req.user?.email || 'Unknown'} - IP: ${req.ip}`);
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

// Input sanitization middleware
const sanitizeInput = (req, res, next) => {
  // Basic XSS protection for string inputs
  const sanitizeString = (str) => {
    if (typeof str !== 'string') return str;
    return str
      .replace(/[<>]/g, '') // Remove < and > characters
      .trim();
  };

  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    for (const key in req.body) {
      if (typeof req.body[key] === 'string') {
        req.body[key] = sanitizeString(req.body[key]);
      }
    }
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    for (const key in req.query) {
      if (typeof req.query[key] === 'string') {
        req.query[key] = sanitizeString(req.query[key]);
      }
    }
  }

  next();
};

// Detect suspicious patterns
const suspiciousPatternDetector = (req, res, next) => {
  const suspiciousPatterns = [
    /(\bor\b|\band\b).*[=<>]/i, // SQL injection patterns
    /<script/i, // XSS patterns
    /javascript:/i, // JavaScript injection
    /\bunion\b.*\bselect\b/i, // SQL union attacks
    /\bdrop\b.*\btable\b/i, // SQL drop table
    /\binsert\b.*\binto\b/i, // SQL insert
    /\bdelete\b.*\bfrom\b/i // SQL delete
  ];

  const checkForPatterns = (obj, path = '') => {
    if (typeof obj === 'string') {
      for (const pattern of suspiciousPatterns) {
        if (pattern.test(obj)) {
          console.log(`[SECURITY ALERT] Suspicious pattern detected in ${path}: ${obj.substring(0, 100)}`);
          console.log(`[SECURITY ALERT] IP: ${req.ip}, User-Agent: ${req.get('User-Agent')}`);
          return true;
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const key in obj) {
        if (checkForPatterns(obj[key], `${path}.${key}`)) {
          return true;
        }
      }
    }
    return false;
  };

  // Check request body and query parameters
  const suspicious = checkForPatterns(req.body, 'body') || checkForPatterns(req.query, 'query');
  
  if (suspicious) {
    return res.status(400).json({
      success: false,
      message: 'Invalid request format'
    });
  }

  next();
};

// Password strength validator (for registration)
const validatePasswordStrength = (password) => {
  const minLength = 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumbers = /\d/.test(password);
  const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  const score = [
    password.length >= minLength,
    hasUpperCase,
    hasLowerCase,
    hasNumbers,
    hasSpecialChar
  ].filter(Boolean).length;

  return {
    isValid: score >= 4, // Require at least 4 out of 5 criteria
    score,
    feedback: {
      length: password.length >= minLength,
      uppercase: hasUpperCase,
      lowercase: hasLowerCase,
      numbers: hasNumbers,
      specialChar: hasSpecialChar
    }
  };
};

module.exports = {
  authRateLimit,
  adminRateLimit,
  securityLogger,
  sanitizeInput,
  suspiciousPatternDetector,
  validatePasswordStrength
};
