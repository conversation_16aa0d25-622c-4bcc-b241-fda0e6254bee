{"name": "luxe-fashion-backend", "version": "1.0.0", "description": "Backend API for Luxe Fashion e-commerce website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "frontend": "node frontend-server.js", "test": "jest", "db:migrate": "node database/migrate.js", "db:seed": "node database/seed.js", "db:setup": "node setup-database.js", "db:test": "node test-db-connection.js"}, "keywords": ["ecommerce", "fashion", "api", "nodejs", "express", "mysql"], "author": "Luxe Fashion Team", "license": "MIT", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.9.7", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0"}}