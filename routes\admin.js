const express = require('express');
const router = express.Router();

const {
  getDashboardAnalytics,
  getUsers,
  updateUserRole,
  deleteUser,
  getOrders,
  updateOrderStatus
} = require('../controllers/adminController');

const {
  createProduct,
  updateProduct,
  deleteProduct,
  getProducts
} = require('../controllers/productController');

const { protect, authorize } = require('../middleware/auth');
const { validateId, validatePagination } = require('../middleware/validation');
const { adminRateLimit } = require('../middleware/security');

// All admin routes require authentication and admin role
router.use(adminRateLimit);
router.use(protect);
router.use(authorize('admin', 'super_admin'));

// Dashboard analytics
router.get('/dashboard', getDashboardAnalytics);

// User management
router.get('/users', validatePagination, getUsers);
router.put('/users/:id/role', validateId, updateUserRole);
router.delete('/users/:id', validateId, deleteUser);

// Order management
router.get('/orders', validatePagination, getOrders);
router.put('/orders/:id/status', validateId, updateOrderStatus);

// Product management (admin versions with different permissions)
router.get('/products', validatePagination, getProducts);
router.post('/products', createProduct);
router.put('/products/:id', validateId, updateProduct);
router.delete('/products/:id', validateId, deleteProduct);

module.exports = router;
