const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function testAuthenticationIssues() {
    console.log('🔍 Diagnosing Authentication Issues');
    console.log('===================================\n');

    try {
        // Test 1: Admin Login
        console.log('1️⃣ Testing Admin Login...');
        const adminLogin = await axios.post(`${BASE_URL}/api/auth/login`, {
            email: '<EMAIL>',
            password: 'admin123'
        });
        
        if (adminLogin.status === 200 && adminLogin.data.success) {
            console.log('✅ Admin login successful');
            console.log(`   Token: ${adminLogin.data.data.token.substring(0, 30)}...`);
            console.log(`   Role: ${adminLogin.data.data.user.role}`);
            
            const adminToken = adminLogin.data.data.token;
            
            // Test 2: Access protected user route
            console.log('\n2️⃣ Testing protected user route (/api/auth/me)...');
            try {
                const meResponse = await axios.get(`${BASE_URL}/api/auth/me`, {
                    headers: { Authorization: `Bearer ${adminToken}` }
                });
                console.log('✅ Protected route access successful');
                console.log(`   User ID: ${meResponse.data.data.id}`);
                console.log(`   Email: ${meResponse.data.data.email}`);
            } catch (error) {
                console.log('❌ Protected route access failed');
                console.log(`   Status: ${error.response?.status}`);
                console.log(`   Error: ${error.response?.data?.message}`);
            }
            
            // Test 3: Access admin route
            console.log('\n3️⃣ Testing admin route (/api/admin/dashboard)...');
            try {
                const dashboardResponse = await axios.get(`${BASE_URL}/api/admin/dashboard`, {
                    headers: { Authorization: `Bearer ${adminToken}` }
                });
                console.log('✅ Admin route access successful');
                console.log(`   Dashboard data available: ${!!dashboardResponse.data.data}`);
            } catch (error) {
                console.log('❌ Admin route access failed');
                console.log(`   Status: ${error.response?.status}`);
                console.log(`   Error: ${error.response?.data?.message}`);
            }
            
            // Test 4: Test regular user login
            console.log('\n4️⃣ Testing regular user registration and access...');
            try {
                const userEmail = `testuser${Date.now()}@example.com`;
                const userRegister = await axios.post(`${BASE_URL}/api/auth/register`, {
                    email: userEmail,
                    password: 'TestPassword123',  // Fixed: Added uppercase letter
                    first_name: 'Test',
                    last_name: 'User'
                });

                if (userRegister.status === 201) {
                    console.log('✅ User registration successful');
                    const userToken = userRegister.data.data.token;
                    console.log(`   User role: ${userRegister.data.data.user.role}`);

                    // Test user access to admin route (should fail)
                    try {
                        await axios.get(`${BASE_URL}/api/admin/dashboard`, {
                            headers: { Authorization: `Bearer ${userToken}` }
                        });
                        console.log('❌ SECURITY ISSUE: Regular user can access admin routes!');
                    } catch (error) {
                        if (error.response?.status === 403) {
                            console.log('✅ Authorization working: User correctly denied admin access');
                        } else {
                            console.log(`⚠️  Unexpected error: ${error.response?.status} - ${error.response?.data?.message}`);
                        }
                    }
                } else {
                    console.log('❌ User registration failed');
                }
            } catch (error) {
                console.log('❌ User registration failed');
                console.log(`   Status: ${error.response?.status}`);
                console.log(`   Error: ${error.response?.data?.message}`);
                if (error.response?.data?.errors) {
                    console.log('   Validation errors:');
                    error.response.data.errors.forEach(err => {
                        console.log(`     - ${err.msg} (${err.param})`);
                    });
                }
            }
            
        } else {
            console.log('❌ Admin login failed');
        }
        
        // Test 5: Test invalid token
        console.log('\n5️⃣ Testing invalid token...');
        try {
            await axios.get(`${BASE_URL}/api/auth/me`, {
                headers: { Authorization: 'Bearer invalid_token_here' }
            });
            console.log('❌ SECURITY ISSUE: Invalid token accepted!');
        } catch (error) {
            if (error.response?.status === 401) {
                console.log('✅ Token validation working: Invalid token correctly rejected');
            } else {
                console.log(`⚠️  Unexpected error: ${error.response?.status}`);
            }
        }
        
        // Test 6: Test missing token
        console.log('\n6️⃣ Testing missing token...');
        try {
            await axios.get(`${BASE_URL}/api/auth/me`);
            console.log('❌ SECURITY ISSUE: Missing token accepted!');
        } catch (error) {
            if (error.response?.status === 401) {
                console.log('✅ Authentication working: Missing token correctly rejected');
            } else {
                console.log(`⚠️  Unexpected error: ${error.response?.status}`);
            }
        }
        
    } catch (error) {
        console.log('❌ Initial admin login failed');
        console.log(`   Status: ${error.response?.status}`);
        console.log(`   Error: ${error.response?.data?.message || error.message}`);
        
        if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 Server is not running. Start it with: npm start');
        }
    }
    
    console.log('\n📊 Authentication Diagnosis Complete');
}

testAuthenticationIssues().catch(console.error);
