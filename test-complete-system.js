const http = require('http');

// Test user registration
async function testUserRegistration() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            email: `testuser${Date.now()}@example.com`,
            password: 'TestPassword123',
            first_name: 'Test',
            last_name: 'User'
        });

        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/auth/register',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    if (res.statusCode === 201 && jsonData.success) {
                        resolve({
                            success: true,
                            user: jsonData.data.user,
                            token: jsonData.data.token
                        });
                    } else {
                        reject(new Error(jsonData.message || 'Registration failed'));
                    }
                } catch (e) {
                    reject(new Error('Invalid response format'));
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.write(postData);
        req.end();
    });
}

// Test admin login
async function testAdminLogin() {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            email: '<EMAIL>',
            password: 'admin123'
        });

        const options = {
            hostname: 'localhost',
            port: 3001,
            path: '/api/auth/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                try {
                    const jsonData = JSON.parse(data);
                    if (res.statusCode === 200 && jsonData.success) {
                        resolve({
                            success: true,
                            user: jsonData.data.user,
                            token: jsonData.data.token
                        });
                    } else {
                        reject(new Error(jsonData.message || 'Login failed'));
                    }
                } catch (e) {
                    reject(new Error('Invalid response format'));
                }
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        req.write(postData);
        req.end();
    });
}

// Test endpoint with token
function testEndpoint(path, token, description) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3001,
            path: path,
            method: 'GET',
            headers: token ? {
                'Authorization': `Bearer ${token}`
            } : {}
        };

        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => {
                data += chunk;
            });
            res.on('end', () => {
                console.log(`${description}: ${res.statusCode === 200 ? '✅' : '❌'} (${res.statusCode})`);
                resolve(res.statusCode === 200);
            });
        });

        req.on('error', (err) => {
            console.log(`${description}: ❌ (${err.message})`);
            resolve(false);
        });

        req.end();
    });
}

// Test frontend endpoints
function testFrontendEndpoint(path, description) {
    return new Promise((resolve) => {
        const options = {
            hostname: 'localhost',
            port: 3003,
            path: path,
            method: 'GET'
        };

        const req = http.request(options, (res) => {
            console.log(`${description}: ${res.statusCode === 200 ? '✅' : '❌'} (${res.statusCode})`);
            resolve(res.statusCode === 200);
        });

        req.on('error', (err) => {
            console.log(`${description}: ❌ (${err.message})`);
            resolve(false);
        });

        req.end();
    });
}

async function runCompleteSystemTest() {
    console.log('🚀 Running Complete System Test');
    console.log('================================');

    let allTestsPassed = true;

    try {
        // Test 1: User Registration
        console.log('\n📝 Testing User Registration...');
        const newUser = await testUserRegistration();
        console.log(`✅ User registration successful: ${newUser.user.email}`);

        // Test 2: Admin Login
        console.log('\n🔐 Testing Admin Login...');
        const adminLogin = await testAdminLogin();
        console.log(`✅ Admin login successful: ${adminLogin.user.email} (${adminLogin.user.role})`);

        // Test 3: Backend API Endpoints
        console.log('\n🔧 Testing Backend API Endpoints...');
        const backendTests = [
            await testEndpoint('/health', null, '   Health Check'),
            await testEndpoint('/api/products', null, '   Products API'),
            await testEndpoint('/api/auth/me', newUser.token, '   User Profile'),
            await testEndpoint('/api/admin/dashboard', adminLogin.token, '   Admin Dashboard'),
            await testEndpoint('/api/admin/users', adminLogin.token, '   Admin Users'),
            await testEndpoint('/api/admin/orders', adminLogin.token, '   Admin Orders')
        ];

        // Test 4: Frontend Endpoints
        console.log('\n🎨 Testing Frontend Endpoints...');
        const frontendTests = [
            await testFrontendEndpoint('/', '   Homepage'),
            await testFrontendEndpoint('/products.html', '   Products Page'),
            await testFrontendEndpoint('/admin.html', '   Admin Dashboard'),
            await testFrontendEndpoint('/api-config.js', '   API Configuration'),
            await testFrontendEndpoint('/admin.js', '   Admin JavaScript')
        ];

        // Calculate results
        const backendPassed = backendTests.filter(Boolean).length;
        const frontendPassed = frontendTests.filter(Boolean).length;
        
        console.log('\n📊 Test Results Summary:');
        console.log(`   Backend API: ${backendPassed}/${backendTests.length} tests passed`);
        console.log(`   Frontend: ${frontendPassed}/${frontendTests.length} tests passed`);

        if (backendPassed === backendTests.length && frontendPassed === frontendTests.length) {
            console.log('\n🎉 ALL TESTS PASSED! System is fully functional!');
        } else {
            console.log('\n⚠️  Some tests failed. Please check the issues above.');
            allTestsPassed = false;
        }

    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        allTestsPassed = false;
    }

    console.log('\n🌐 System URLs:');
    console.log('   Frontend: http://localhost:3003');
    console.log('   Admin Dashboard: http://localhost:3003/admin.html');
    console.log('   Backend API: http://localhost:3001');
    console.log('\n👤 Admin Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123');

    return allTestsPassed;
}

runCompleteSystemTest().catch(console.error);
